<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Y-WebSocket Client Example</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 20px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .controls {
            margin: 20px 0;
        }
        .controls input, .controls button {
            margin: 5px;
            padding: 8px 12px;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .stats {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Y-WebSocket Client Example</h1>
    
    <div class="controls">
        <label>
            Document ID: 
            <input type="text" id="documentId" value="demo-document" placeholder="Enter document ID">
        </label>
        <label>
            User ID: 
            <input type="text" id="userId" value="" placeholder="Enter your name">
        </label>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
    </div>

    <div id="status" class="status disconnected">Disconnected</div>
    
    <div class="container">
        <h3>Shared Text Document</h3>
        <textarea id="textArea" placeholder="Connect to start editing..." disabled></textarea>
        <p><small>Changes are synchronized in real-time across all connected clients</small></p>
    </div>

    <div class="container">
        <h3>Shared Array</h3>
        <div>
            <input type="text" id="arrayInput" placeholder="Add item to shared array">
            <button onclick="addToArray()">Add Item</button>
            <button onclick="clearArray()">Clear Array</button>
        </div>
        <div id="arrayDisplay" class="stats">Array is empty</div>
    </div>

    <div class="stats" id="stats">
        <strong>Statistics:</strong><br>
        Document State: Not connected<br>
        Provider Status: Disconnected
    </div>

    <div id="info">
        <h3>Instructions:</h3>
        <ul>
            <li>Enter a document ID and your name</li>
            <li>Click "Connect" to join the collaborative session</li>
            <li>Open this page in multiple tabs to see real-time synchronization</li>
            <li>Edit the text area or add items to the array</li>
            <li>All changes are automatically synchronized using YJS</li>
        </ul>
    </div>

    <!-- Load YJS and y-websocket from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/yjs@13.6.10/dist/yjs.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/y-websocket@3.0.0/dist/y-websocket.cjs"></script>

    <script>
        let provider = null;
        let doc = null;
        let yText = null;
        let yArray = null;
        let isUpdating = false;

        // Set default user ID
        document.getElementById('userId').value = `User-${Math.random().toString(36).substr(2, 5)}`;

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function updateStats() {
            const statsEl = document.getElementById('stats');
            if (doc && provider) {
                const clientId = doc.clientID;
                const docSize = Y.encodeStateAsUpdate(doc).length;
                statsEl.innerHTML = `
                    <strong>Statistics:</strong><br>
                    Client ID: ${clientId}<br>
                    Document Size: ${docSize} bytes<br>
                    Provider Status: ${provider.wsconnected ? 'Connected' : 'Disconnected'}<br>
                    Synced: ${provider.synced ? 'Yes' : 'No'}
                `;
            } else {
                statsEl.innerHTML = `
                    <strong>Statistics:</strong><br>
                    Document State: Not connected<br>
                    Provider Status: Disconnected
                `;
            }
        }

        function connect() {
            const documentId = document.getElementById('documentId').value.trim();
            const userId = document.getElementById('userId').value.trim();

            if (!documentId) {
                alert('Please enter a document ID');
                return;
            }

            if (!userId) {
                alert('Please enter your name');
                return;
            }

            // Disconnect existing connection
            disconnect();

            updateStatus('connecting', 'Connecting...');

            try {
                // Create YJS document
                doc = new Y.Doc();

                // Get shared types
                yText = doc.getText('content');
                yArray = doc.getArray('items');

                // Create WebSocket provider
                const wsUrl = `ws://localhost:3000/yjs?room=${encodeURIComponent(documentId)}&userId=${encodeURIComponent(userId)}`;
                provider = new WebsocketProvider(wsUrl, documentId, doc);

                // Handle provider events
                provider.on('status', event => {
                    console.log('Provider status:', event.status);
                    if (event.status === 'connected') {
                        updateStatus('connected', `Connected to "${documentId}" as ${userId}`);
                    } else if (event.status === 'disconnected') {
                        updateStatus('disconnected', 'Disconnected');
                    }
                    updateStats();
                });

                provider.on('sync', isSynced => {
                    console.log('Sync status:', isSynced);
                    updateStats();
                });

                // Setup text synchronization
                const textArea = document.getElementById('textArea');
                textArea.disabled = false;

                // Update textarea when YJS text changes
                yText.observe(event => {
                    if (!isUpdating) {
                        isUpdating = true;
                        textArea.value = yText.toString();
                        isUpdating = false;
                    }
                });

                // Update YJS text when textarea changes
                textArea.addEventListener('input', () => {
                    if (!isUpdating) {
                        isUpdating = true;
                        const currentText = yText.toString();
                        const newText = textArea.value;
                        
                        // Simple diff and apply changes
                        if (currentText !== newText) {
                            yText.delete(0, currentText.length);
                            yText.insert(0, newText);
                        }
                        isUpdating = false;
                    }
                });

                // Setup array synchronization
                yArray.observe(() => {
                    updateArrayDisplay();
                });

                // Initialize displays
                textArea.value = yText.toString();
                updateArrayDisplay();
                updateStats();

                console.log('Connected to document:', documentId);

            } catch (error) {
                console.error('Failed to connect:', error);
                updateStatus('disconnected', 'Connection failed');
                alert('Failed to connect: ' + error.message);
            }
        }

        function disconnect() {
            if (provider) {
                provider.destroy();
                provider = null;
            }

            if (doc) {
                doc.destroy();
                doc = null;
            }

            yText = null;
            yArray = null;

            const textArea = document.getElementById('textArea');
            textArea.disabled = true;
            textArea.value = '';

            updateStatus('disconnected', 'Disconnected');
            updateArrayDisplay();
            updateStats();
        }

        function addToArray() {
            const input = document.getElementById('arrayInput');
            const value = input.value.trim();
            
            if (value && yArray) {
                yArray.push([{
                    text: value,
                    timestamp: new Date().toISOString(),
                    user: document.getElementById('userId').value
                }]);
                input.value = '';
            }
        }

        function clearArray() {
            if (yArray) {
                yArray.delete(0, yArray.length);
            }
        }

        function updateArrayDisplay() {
            const display = document.getElementById('arrayDisplay');
            if (yArray && yArray.length > 0) {
                const items = yArray.toArray().map((item, index) => 
                    `${index + 1}. "${item.text}" (by ${item.user} at ${new Date(item.timestamp).toLocaleTimeString()})`
                ).join('<br>');
                display.innerHTML = items;
            } else {
                display.innerHTML = 'Array is empty';
            }
        }

        // Handle page unload
        window.addEventListener('beforeunload', disconnect);

        // Update stats periodically
        setInterval(updateStats, 1000);
    </script>
</body>
</html>
